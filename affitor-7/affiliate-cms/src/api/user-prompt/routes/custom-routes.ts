/**
 * Custom routes for user-prompt
 * These routes provide additional functionality beyond the standard CRUD operations
 */

export default {
  routes: [
    // Toggle favorite status
    {
      method: 'POST',
      path: '/user-prompts/:id/favorite',
      handler: 'user-prompt.toggleFavorite',
      config: {
        policies: ['plugin::users-permissions.isAuthenticated'],
      },
    },
    
    // Increment usage count
    {
      method: 'POST',
      path: '/user-prompts/:id/use',
      handler: 'user-prompt.incrementUsage',
      config: {
        policies: ['plugin::users-permissions.isAuthenticated'],
      },
    },
    
    // Get recent prompts (last 10 used)
    {
      method: 'GET',
      path: '/user-prompts/recent',
      handler: 'user-prompt.getRecent',
      config: {
        policies: ['plugin::users-permissions.isAuthenticated'],
      },
    },
    
    // Get user's available tags
    {
      method: 'GET',
      path: '/user-prompts/tags',
      handler: 'user-prompt.getTags',
      config: {
        policies: ['plugin::users-permissions.isAuthenticated'],
      },
    },
  ],
};
